import "dotenv/config";
import "reflect-metadata";

import { DataSource } from "typeorm";
import express from "express";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { SqlDatabase } from "langchain/sql_db";
import { createSqlQueryChain } from "langchain/chains/sql_db";
import { ConversationSummaryBufferMemory } from "langchain/memory";
import { ConversationChain } from "langchain/chains";
import { PromptTemplate } from "@langchain/core/prompts";
import { z } from "zod";
import { StructuredOutputParser } from "@langchain/core/output_parsers";
import { RunnableSequence } from "@langchain/core/runnables";

const app = express();
app.use(express.json());

// 1. Create TypeORM DataSource
const dataSource = new DataSource({
  type: "postgres",
  url: process.env.DATABASE_URL,
  synchronize: false,
  entities: [
    "User",
    "Vehicle",
    "ServiceRequest",
    "ServiceRequestProvider",
    "ProvidedService",
    "ProvidedServiceAvailability",
    "ProvidedServiceAvailabilitySlot",
    "ProvidedServiceCancellationPolicy",
    "ProvidedServiceType",
  ],
  ssl: true,
  extra: {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  },
  logging: true,
  keepConnectionAlive: true,
});
await dataSource.initialize();
console.log("🔌 Database connected!");
// 2. Create SqlDatabase from DataSource
const db = await SqlDatabase.fromDataSourceParams({
  appDataSource: dataSource,
  includesTables: [
    "User",
    "Vehicle",
    "ServiceRequest",
    "ServiceRequestProvider",
    "ProvidedService",
    "ProvidedServiceAvailability",
    "ProvidedServiceAvailabilitySlot",
    "ProvidedServiceCancellationPolicy",
    "ProvidedServiceType",
  ],
  customDescriptions: {
    Vehicle: `Contains vehicle details for a user. Includes userId, vin, plateNumber, milage, model, engine, etc.`,
    ServiceRequest:
      "ServiceRequest are the requests made by a customer for a service.",
    ServiceRequestProvider: `ServiceRequestProvider are the providers request of a service it's a request created by customer and after accept by provider we add providerId to the ServiceRequest`,
    ProvidedService: `Tracks each service the provider offers, belongs to a user.`,
    ProvidedServiceAvailability: `Defines days when a provider is available.`,
    ProvidedServiceAvailabilitySlot: `Defines time slots for availability within a day.`,
    ProvidedServiceCancellationPolicy: `Defines cancellation fee and window for a service.`,
    ProvidedServiceType: `Links provided services to actual service types and includes pricing.`,
  },
});
console.log("🔌 DB store fetch!");

// 3. Initialize memory store
const memoryMap = new Map();
const sqlSchema = z.object({
  SQLQuery: z.string(),
  SQLResult: z.array(z.record(z.any())),
  Answer: z.string(),
});

// Function to clean SQL query from markdown formatting
const cleanSqlQuery = (query) => {
  if (typeof query !== "string") return query;

  // Remove markdown code blocks (```sql ... ```)
  let cleaned = query.replace(/```sql\s*/gi, "").replace(/```\s*$/gi, "");

  // Remove any leading/trailing whitespace
  cleaned = cleaned.trim();

  // Remove any remaining markdown formatting
  cleaned = cleaned.replace(/^`+/, "").replace(/`+$/, "");

  return cleaned;
};

const usingRunner = async ({ question, history }) => {
  const parser = StructuredOutputParser.fromZodSchema(sqlSchema);
  const formatInstructions = parser.getFormatInstructions();
  console.log(formatInstructions);
  const structuredSqlPrompt = new PromptTemplate({
    inputVariables: [
      "question",
      "schema",
      "userId",
      "history",
      "formatInstructions",
    ],
    template: `You are a PostgreSQL expert assistant.

Only use the following tables and sample data:
{schema}

Instructions:
- Wrap all column names in double quotes ("")
- Never use SELECT *
- LIMIT results to 5 rows unless specified
- Use WHERE "userId" = {userId} OR "providerId" = {userId} when relevant
- For service requests, ALWAYS filter by date >= '2025-07-01' to exclude past services
- Avoid columns not in schema
- Respect table relationships based on foreign keys
- If showing provider information, JOIN the "ServiceRequest" table's "providerId" to the "User" table's "id" and include the provider's "firstName" and "lastName" in the result instead of providerId

Respond ONLY in this JSON format:
{formatInstructions}

Chat History: {history}
User Question: {question}`,
  });
  const sqlChain = RunnableSequence.from([
    async (input) => ({
      question: input.question,
      schema: input.schema,
      userId: input.userId,
      history: input.history,
      formatInstructions: input.formatInstructions,
    }),
    structuredSqlPrompt,
    naturalLLM,
    parser,
  ]);
  const result = await sqlChain.invoke({
    question,
    userId,
    history,
    formatInstructions,
    schema: await db.getTableInfo(),
  });
};

// 4. Handle routes
app.post("/", async (req, res) => {
  const { question, sessionId = "default" } = req.body;
  // const userId = 78;
  // const userId = 137;
  const userId = 153;

  // Memory setup per session
  let memory = memoryMap.get(sessionId);
  if (!memory) {
    memory = new ConversationSummaryBufferMemory({
      llm: new ChatGoogleGenerativeAI({
        model: "gemini-2.0-pro",
        apiKey: process.env.GOOGLE_API_KEY,
        temperature: 0,
      }),
      memoryKey: "history",
      returnMessages: true,
      maxTokenLimit: 1000,
    });
    memoryMap.set(sessionId, memory);
    console.log("New memory created for session:", sessionId);
  }
  // SQL LLM chain with memory
  const naturalLLM = new ChatGoogleGenerativeAI({
    model: "gemini-2.0-flash",
    temperature: 0,
    apiKey: process.env.GOOGLE_API_KEY,
    verbose: true,
  });
  const sqlChain = await createSqlQueryChain({
    llm: naturalLLM,
    db,
    memory,
    dialect: "postgres",
    verbose: true,
  });
  const { history } = await memory.loadMemoryVariables({});

  // Classify if query is SQL
  const classificationPrompt = `
	You are classifying whether a user's question needs a database query.
	
	Chat History:
	${history}
	
	User asked: "${question}"
	Does this require a database query to answer? Reply with only "yes" or "no".
	`;
  const classifier = new ChatGoogleGenerativeAI({ model: "gemini-2.0-flash" });
  const classificationResult = await classifier.invoke(classificationPrompt);
  const isSQLQuery = classificationResult.content.toLowerCase().includes("yes");

  if (isSQLQuery) {
    const contextualPrompt = `You are **Revi**, a smart, friendly, and expert AI assistant built into the **Revi** app — designed to help vehicle owners get real-time diagnostics, guidance, and support 24/7 without visiting a shop.
		🎯 Your Role:
		Act like a knowledgeable **automotive service advisor**. Help users understand, troubleshoot, and resolve their car-related issues with confidence and clarity.

		Chat History:
		${history}
		User question: "${question}"
    As Revi, respond with a database query that helps answer the user's question, considering the full conversation.

    Important filtering rules:
    - Only show service requests with dates >= '2025-07-01' (current date or future dates)
    - Be sure to include results where "userId" = ${userId} OR "providerId" = ${userId} when relevant
    - If showing provider information, join the ServiceRequest's providerId to the User table's id and include the provider's firstName and lastName in the result instead of providerId
    - When filtering by date, use appropriate date column names from the schema (like "scheduledDate", "appointmentDate", "createdAt", etc.)`;

    const result = await sqlChain.invoke({ question: contextualPrompt });
    console.log("🧠 Result:", result);
    console.log("🧠 Result Type:", typeof result);

    // Clean the SQL query from markdown formatting
    const sqlQuery = cleanSqlQuery(result);

    console.log("🧠 SQL Query:", sqlQuery);
    const queryResults = await db.run(sqlQuery);

    console.log("🧠 SQL Execution Result:", queryResults);
    const summaryPrompt = `
    You are Revi, an AI automotive assistant. Summarize the following database result into a helpful response for the user.

    User Question: "${question}"
    SQL Query (do not include in your response): ${sqlQuery}
    Result: ${JSON.stringify(queryResults)}
    Current User ID: ${userId}

    Guidelines:
    - Only show service requests that are current or future (not past dates)
    - When describing a service request, check if the providerId matches the current user ID (${userId}):
      * If providerId = ${userId}, say "You are assigned as the provider" or "You are providing this service"
      * If providerId is different from ${userId}, use the provider's first and last name from the User table
      * If the provider's name is not available, say "a provider" or "no provider assigned"
    - If the result includes appointment details, summarize the most important information like the date, status, type of service, and provider assignment as above
    - If the service request status is "completed", clearly state that the service has been completed (use past tense), and mention the completion date if available
    - If the status is not "completed", describe the current status and expected completion as appropriate (use present or future tense)
    - If there's no relevant data or all services are in the past, politely let the user know they have no current or upcoming service requests

    Respond in a clear and friendly tone. Avoid using IDs or technical terms like "id" or "record". Instead, describe the data naturally.

    Response:
    `;
    const response = await naturalLLM.invoke(summaryPrompt);
    console.log("🗣️ Final Response:", response.content);
    res.json({ message: response.content });
  } else {
    const naturalPrompt = `
You are Revi, the AI automotive assistant for Revilo - a mobile car service platform that brings certified technicians to customers' locations.
Your Role:
Provide 24/7 automotive support, diagnostics, and guidance.
Help with emergencies, maintenance questions, and service coordination.
Connect users with Revilo's mobile technician network when needed.

Communication Style:
Friendly, reliable, and solution-focused.
Use simple language, avoid technical jargon.
Prioritize safety in all recommendations.
Be concise but thorough.
Only introduce yourself in the first message of a conversation, not for follow-up questions

Key Guidelines:
For emergencies: Ensure user safety first, then provide immediate help.
For diagnostics: Ask relevant questions, explain issues clearly.
For service needs: Coordinate mobile technician visits.
Always escalate complex repairs or safety-critical issues to professionals.
Never recommend unsafe DIY fixes.
After 3-4 continuous Q&A exchanges, offer: "Would you like to connect with a certified provider who can take a closer look over a virtual call and guide you through the next steps?"

Revi's Promise:
Fast, transparent, reliable automotive services delivered to your location. No delays, no surprises, just expert car care made easy.

Chat History:
{history}

User: {input}
Revi:`;

    const conversationChain = new ConversationChain({
      llm: naturalLLM,
      memory,
      prompt: new PromptTemplate({
        template: naturalPrompt,
        inputVariables: ["history", "input"],
      }),
    });
    const naturalResult = await conversationChain.call({ input: question });
    console.log("🧠 Natural Response:", naturalResult.response);
    res.json({ message: naturalResult.response });
  }

  console.log("✅ Execution done.");
});

app.listen(3000, () => {
  console.log("Server is running on port 3000");
});
